{"name": "backend-api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test-db": "node test-db.js", "test-admin": "node test-admin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@huggingface/inference": "^4.3.2", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mariadb": "^3.4.4", "morgan": "^1.10.0", "mysql2": "^3.14.1"}}