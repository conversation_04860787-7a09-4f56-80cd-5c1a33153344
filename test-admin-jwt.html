<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin JWT Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #c82333;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .token-display {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👨‍💼 Test Admin JWT Authentication</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Admin Email:</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter password" required>
            </div>
            
            <button type="submit">🚀 Test Admin Login</button>
            <button type="button" class="btn-secondary" onclick="testSessionValidation()">🔍 Test Session Validation</button>
            <button type="button" class="btn-danger" onclick="testLogout()">🚪 Test Logout</button>
        </form>
        
        <div id="tokenDisplay" class="token-display" style="display: none;">
            <strong>Current JWT Token:</strong><br>
            <span id="tokenValue"></span>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        let currentToken = null;

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="result info">🔄 Testing admin login...</div>';
            
            try {
                console.log('🚀 Testing admin login...');
                
                const response = await fetch('http://localhost:3000/api/v1/adminauth/login-admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });

                const data = await response.json();
                console.log('📋 Response:', data);

                if (data.success) {
                    currentToken = data.token;
                    displayToken(data.token);
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Admin login successful!<br>
                            Admin: ${data.data.FullName}<br>
                            Role: ${data.data.Role}<br>
                            Email: ${data.data.Email}<br>
                            JWT Token: ${data.token ? 'Received ✅' : 'Missing ❌'}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Admin login failed: ${data.error}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('❌ Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        });

        async function testSessionValidation() {
            const resultDiv = document.getElementById('result');
            
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="result error">❌ No token available. Please login first.</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result info">🔄 Testing session validation...</div>';
            
            try {
                console.log('🔍 Testing session validation...');
                
                const response = await fetch('http://localhost:3000/api/v1/adminauth/validate-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        token: currentToken
                    })
                });

                const data = await response.json();
                console.log('📋 Validation Response:', data);

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Session validation successful!<br>
                            Admin: ${data.data.FullName}<br>
                            Role: ${data.data.Role}<br>
                            Status: ${data.data.Status}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Session validation failed: ${data.error}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('❌ Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        }

        async function testLogout() {
            const resultDiv = document.getElementById('result');
            
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="result error">❌ No token available. Please login first.</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result info">🔄 Testing logout...</div>';
            
            try {
                console.log('🚪 Testing logout...');
                
                const response = await fetch('http://localhost:3000/api/v1/adminauth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        token: currentToken
                    })
                });

                const data = await response.json();
                console.log('📋 Logout Response:', data);

                if (data.success) {
                    currentToken = null;
                    hideToken();
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Logout successful!<br>
                            Token has been blacklisted.
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Logout failed: ${data.error}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('❌ Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        }

        function displayToken(token) {
            if (token) {
                document.getElementById('tokenValue').textContent = token;
                document.getElementById('tokenDisplay').style.display = 'block';
            }
        }

        function hideToken() {
            document.getElementById('tokenDisplay').style.display = 'none';
        }
    </script>
</body>
</html>
