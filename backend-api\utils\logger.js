const axios = require('axios');

class WeatherLogger {
  constructor() {
    this.colors = {
      reset: '\x1b[0m',
      bright: '\x1b[1m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      magenta: '\x1b[35m',
      cyan: '\x1b[36m',
      white: '\x1b[37m'
    };
  }

  log(message, color = 'white') {
    const timestamp = new Date().toLocaleString();
    const colorCode = this.colors[color] || this.colors.white;
    console.log(`${colorCode}[${timestamp}] ${message}${this.colors.reset}`);
  }

  success(message) {
    this.log(`✅ ${message}`, 'green');
  }

  error(message) {
    this.log(`❌ ${message}`, 'red');
  }

  warning(message) {
    this.log(`⚠️  ${message}`, 'yellow');
  }

  info(message) {
    this.log(`ℹ️  ${message}`, 'blue');
  }

  weather(message) {
    this.log(`🌤️  ${message}`, 'cyan');
  }

  server(message) {
    this.log(`🚀 ${message}`, 'magenta');
  }

  async testWeatherAPI() {
    try {
      this.info('Testing OpenWeatherMap API connection...');
      
      const apiKey = process.env.OPENWEATHER_API_KEY;
      
      if (!apiKey) {
        this.error('OPENWEATHER_API_KEY not found in environment variables');
        return false;
      }

      const city = 'Cebu City,PH';
      const url = `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${apiKey}&units=metric`;
      
      const response = await axios.get(url, { timeout: 5000 });
      const data = response.data;
      
      this.success('OpenWeatherMap API connection successful!');
      this.weather(`Current weather: ${Math.round(data.main.temp)}°C, ${data.weather[0].description} in ${data.name}`);
      this.info(`Humidity: ${data.main.humidity}% | Pressure: ${data.main.pressure} hPa`);
      
      return true;
      
    } catch (error) {
      this.error(`OpenWeatherMap API test failed: ${error.message}`);
      
      if (error.response) {
        this.error(`API Response: ${error.response.status} - ${error.response.data?.message || 'Unknown error'}`);
      } else if (error.code === 'ENOTFOUND') {
        this.error('Network connection failed - Check internet connection');
      } else if (error.code === 'ECONNABORTED') {
        this.error('API request timeout - OpenWeatherMap may be slow');
      }
      
      return false;
    }
  }

  logServerStart(port) {
    console.log('\n' + '='.repeat(60));
    this.server(`Backend API Server started successfully on port ${port}`);
    this.info(`Health check: http://localhost:${port}/`);
    this.info(`Weather API: http://localhost:${port}/api/v1/weather`);
    this.info(`API Documentation: http://localhost:${port}/api`);
    console.log('='.repeat(60) + '\n');
  }

  logWeatherEndpointAccess(ip, userAgent) {
    this.weather(`Weather API accessed from ${ip}`);
    if (userAgent && userAgent.includes('Angular')) {
      this.info('Request from Angular frontend detected');
    }
  }

  logWeatherAPIError(error, fallback = false) {
    this.error(`Weather API error: ${error.message}`);
    if (fallback) {
      this.warning('Returning fallback weather data to client');
    }
  }

  logWeatherAPISuccess(temperature, location) {
    this.weather(`Weather data retrieved: ${temperature}°C in ${location}`);
  }
}

module.exports = new WeatherLogger();
